#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频制作工具启动器
解决编码问题并提供更好的错误处理
"""

import sys
import os
import locale

def main():
    """主函数"""
    try:
        # 设置控制台编码
        if sys.platform.startswith('win'):
            # Windows系统设置UTF-8编码
            os.system('chcp 65001 >nul 2>&1')
            
        # 设置locale
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
            except:
                pass  # 忽略locale设置失败
        
        print("正在启动视频制作工具...")
        
        # 导入并运行主程序
        from 视频制作工具 import VideoMaker
        import tkinter as tk
        
        root = tk.Tk()
        app = VideoMaker(root)
        
        print("视频制作工具已启动！")
        root.mainloop()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有依赖都已正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
