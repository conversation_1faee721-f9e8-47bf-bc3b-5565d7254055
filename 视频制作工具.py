#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成制作程序
功能：将多张图片合成为视频，支持插入现有视频片段和背景音乐
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import subprocess
import threading
from pathlib import Path
import glob


class VideoMaker:
    def __init__(self, root):
        self.root = root
        self.root.title("视频合成制作工具")
        self.root.geometry("800x600")
        
        # 配置文件路径
        self.config_path = "config.json"
        self.config = {}
        
        # 默认路径
        self.image_folder = "2D Flat Rotatable Acrylic Desktop Ornament with colorful tropical fish design for home decoration, ideal for summer celebration gifts"
        self.insert_video = "1.15-1比1摆件视频+水印.mp4"
        self.background_music = "音乐(1).mp3"
        
        self.setup_ui()
        self.load_config()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置文件区域
        config_frame = ttk.LabelFrame(main_frame, text="配置文件", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(config_frame, text="配置文件路径:").grid(row=0, column=0, sticky=tk.W)
        self.config_path_var = tk.StringVar(value=self.config_path)
        ttk.Entry(config_frame, textvariable=self.config_path_var, width=50).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(config_frame, text="浏览", command=self.browse_config).grid(row=0, column=2, padx=(10, 0))
        ttk.Button(config_frame, text="重新加载", command=self.load_config).grid(row=0, column=3, padx=(10, 0))
        
        # 文件路径区域
        paths_frame = ttk.LabelFrame(main_frame, text="文件路径设置", padding="10")
        paths_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 图片文件夹
        ttk.Label(paths_frame, text="图片文件夹:").grid(row=0, column=0, sticky=tk.W)
        self.image_folder_var = tk.StringVar(value=self.image_folder)
        ttk.Entry(paths_frame, textvariable=self.image_folder_var, width=50).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(paths_frame, text="浏览", command=self.browse_image_folder).grid(row=0, column=2, padx=(10, 0))
        
        # 插入视频
        ttk.Label(paths_frame, text="插入视频:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.insert_video_var = tk.StringVar(value=self.insert_video)
        ttk.Entry(paths_frame, textvariable=self.insert_video_var, width=50).grid(row=1, column=1, padx=(10, 0), pady=(10, 0))
        ttk.Button(paths_frame, text="浏览", command=self.browse_insert_video).grid(row=1, column=2, padx=(10, 0), pady=(10, 0))
        
        # 背景音乐
        ttk.Label(paths_frame, text="背景音乐:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.background_music_var = tk.StringVar(value=self.background_music)
        ttk.Entry(paths_frame, textvariable=self.background_music_var, width=50).grid(row=2, column=1, padx=(10, 0), pady=(10, 0))
        ttk.Button(paths_frame, text="浏览", command=self.browse_background_music).grid(row=2, column=2, padx=(10, 0), pady=(10, 0))
        
        # 视频设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="视频设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 插入视频位置
        ttk.Label(settings_frame, text="插入视频位置:").grid(row=0, column=0, sticky=tk.W)
        self.video_position_var = tk.StringVar(value="开头")
        position_combo = ttk.Combobox(settings_frame, textvariable=self.video_position_var, 
                                    values=["开头", "结尾"], state="readonly", width=20)
        position_combo.grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        
        # 输出文件名
        ttk.Label(settings_frame, text="输出文件名:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.output_filename_var = tk.StringVar(value="output_video.mp4")
        ttk.Entry(settings_frame, textvariable=self.output_filename_var, width=30).grid(row=1, column=1, padx=(10, 0), pady=(10, 0))
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        ttk.Button(control_frame, text="开始制作视频", command=self.start_video_creation).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="预览配置", command=self.preview_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态显示区域
        self.status_text = tk.Text(main_frame, height=15, width=80)
        self.status_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        scrollbar.grid(row=5, column=2, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
    def log_message(self, message):
        """在状态文本框中显示消息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def browse_config(self):
        """浏览配置文件"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.config_path_var.set(filename)
            self.config_path = filename
            
    def browse_image_folder(self):
        """浏览图片文件夹"""
        folder = filedialog.askdirectory(title="选择图片文件夹")
        if folder:
            self.image_folder_var.set(folder)
            self.image_folder = folder
            
    def browse_insert_video(self):
        """浏览插入视频文件"""
        filename = filedialog.askopenfilename(
            title="选择插入视频文件",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        if filename:
            self.insert_video_var.set(filename)
            self.insert_video = filename
            
    def browse_background_music(self):
        """浏览背景音乐文件"""
        filename = filedialog.askopenfilename(
            title="选择背景音乐文件",
            filetypes=[("Audio files", "*.mp3 *.wav *.aac *.m4a"), ("All files", "*.*")]
        )
        if filename:
            self.background_music_var.set(filename)
            self.background_music = filename
            
    def load_config(self):
        """加载配置文件"""
        try:
            config_path = self.config_path_var.get()
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                
                # 更新界面显示
                if 'out_put' in self.config:
                    self.output_filename_var.set(self.config['out_put'])
                if 'videoPositon' in self.config:
                    position = "开头" if self.config['videoPositon'] == 0 else "结尾"
                    self.video_position_var.set(position)
                    
                self.log_message(f"配置文件加载成功: {config_path}")
                self.log_message(f"配置内容: {json.dumps(self.config, indent=2, ensure_ascii=False)}")
            else:
                self.log_message(f"配置文件不存在: {config_path}")
        except Exception as e:
            self.log_message(f"加载配置文件失败: {str(e)}")
            messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")


    def save_config(self):
        """保存配置文件"""
        try:
            # 更新配置
            self.config['out_put'] = self.output_filename_var.get()
            self.config['videoPositon'] = 0 if self.video_position_var.get() == "开头" else 1

            config_path = self.config_path_var.get()
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

            self.log_message(f"配置文件保存成功: {config_path}")
        except Exception as e:
            self.log_message(f"保存配置文件失败: {str(e)}")
            messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")

    def preview_config(self):
        """预览配置信息"""
        try:
            # 获取图片列表
            image_folder = self.image_folder_var.get()
            if os.path.exists(image_folder):
                image_files = []
                for img_num in self.config.get('img_list', []):
                    img_path = os.path.join(image_folder, f"{img_num}.jpg")
                    if os.path.exists(img_path):
                        image_files.append(img_path)

                self.log_message("=== 配置预览 ===")
                self.log_message(f"图片文件夹: {image_folder}")
                self.log_message(f"找到图片文件: {len(image_files)} 张")
                for img in image_files:
                    self.log_message(f"  - {os.path.basename(img)}")

                self.log_message(f"插入视频: {self.insert_video_var.get()}")
                self.log_message(f"背景音乐: {self.background_music_var.get()}")
                self.log_message(f"视频插入位置: {self.video_position_var.get()}")
                self.log_message(f"输出文件: {self.output_filename_var.get()}")
                self.log_message(f"每张图片持续时间: {self.config.get('duration', 2)} 秒")
                self.log_message(f"转场时间: {self.config.get('transition_duration', 0.15)} 秒")
                self.log_message("================")
            else:
                self.log_message(f"图片文件夹不存在: {image_folder}")
        except Exception as e:
            self.log_message(f"预览配置失败: {str(e)}")

    def get_image_files(self):
        """获取要处理的图片文件列表"""
        image_files = []
        image_folder = self.image_folder_var.get()

        if not os.path.exists(image_folder):
            raise FileNotFoundError(f"图片文件夹不存在: {image_folder}")

        # 根据配置文件中的img_list获取图片
        img_list = self.config.get('img_list', [])
        if not img_list:
            # 如果配置文件中没有指定，则获取所有jpg文件
            pattern = os.path.join(image_folder, "*.jpg")
            all_images = glob.glob(pattern)
            all_images.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))
            image_files = all_images
        else:
            # 按照配置文件中指定的顺序获取图片
            for img_num in img_list:
                img_path = os.path.join(image_folder, f"{img_num}.jpg")
                if os.path.exists(img_path):
                    image_files.append(img_path)
                else:
                    self.log_message(f"警告: 图片文件不存在 {img_path}")

        return image_files

    def create_slideshow_video(self, image_files, output_path):
        """创建图片滑动视频"""
        if not image_files:
            raise ValueError("没有找到可用的图片文件")

        # 获取配置参数
        duration = self.config.get('duration', 2)
        transition_duration = self.config.get('transition_duration', 0.15)
        scale = self.config.get('scale', '800:800')
        setdar = self.config.get('setdar', '1/1')

        # 构建FFmpeg命令
        cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件

        # 添加输入图片
        for img in image_files:
            cmd.extend(['-loop', '1', '-t', str(duration), '-i', img])

        # 构建简化的滤镜链 - 实现基本的图片播放和滑动效果
        filter_complex = []

        # 为每张图片添加缩放效果
        for i, _ in enumerate(image_files):
            filter_complex.append(f"[{i}:v]scale={scale},setdar={setdar},fps=30[v{i}]")

        # 连接所有图片
        if len(image_files) > 1:
            concat_inputs = "".join([f"[v{i}]" for i in range(len(image_files))])
            filter_complex.append(f"{concat_inputs}concat=n={len(image_files)}:v=1:a=0[outv]")

            # 添加滤镜复合参数
            cmd.extend(['-filter_complex', ';'.join(filter_complex)])
            cmd.extend(['-map', '[outv]'])
        else:
            # 单张图片的情况
            cmd.extend(['-filter_complex', ';'.join(filter_complex)])
            cmd.extend(['-map', '[v0]'])

        # 输出设置
        cmd.extend(['-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-r', '30'])
        cmd.append(output_path)

        self.log_message(f"创建图片滑动视频...")
        self.log_message(f"FFmpeg命令: {' '.join(cmd)}")

        # 执行FFmpeg命令
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 universal_newlines=True, cwd=os.getcwd())

        _, stderr = process.communicate()

        if process.returncode != 0:
            raise RuntimeError(f"FFmpeg执行失败: {stderr}")

        self.log_message("图片滑动视频创建完成")
        return output_path

    def combine_videos(self, slideshow_path, insert_video_path, output_path):
        """合并滑动视频和插入视频"""
        if not os.path.exists(insert_video_path):
            self.log_message(f"插入视频文件不存在: {insert_video_path}")
            return slideshow_path

        video_position = self.video_position_var.get()
        temp_combined = "temp_combined.mp4"

        try:
            # 获取配置中的分辨率设置
            scale = self.config.get('scale', '800:800')

            if video_position == "开头":
                # 插入视频在开头 - 先统一分辨率和帧率再合并
                cmd = [
                    'ffmpeg', '-y',
                    '-i', insert_video_path,
                    '-i', slideshow_path,
                    '-filter_complex',
                    f'[0:v]scale={scale},fps=30[v0];[1:v]scale={scale},fps=30[v1];[v0][v1]concat=n=2:v=1:a=0[outv]',
                    '-map', '[outv]',
                    '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                    temp_combined
                ]
            else:
                # 插入视频在结尾 - 先统一分辨率和帧率再合并
                cmd = [
                    'ffmpeg', '-y',
                    '-i', slideshow_path,
                    '-i', insert_video_path,
                    '-filter_complex',
                    f'[0:v]scale={scale},fps=30[v0];[1:v]scale={scale},fps=30[v1];[v0][v1]concat=n=2:v=1:a=0[outv]',
                    '-map', '[outv]',
                    '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                    temp_combined
                ]

            self.log_message(f"合并视频 - 插入位置: {video_position}")
            self.log_message(f"FFmpeg命令: {' '.join(cmd)}")

            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                     universal_newlines=True, cwd=os.getcwd())
            _, stderr = process.communicate()

            if process.returncode != 0:
                self.log_message(f"视频合并失败，错误信息: {stderr}")
                raise RuntimeError(f"视频合并失败: {stderr}")

            # 替换原文件
            if os.path.exists(temp_combined):
                if os.path.exists(output_path):
                    os.remove(output_path)
                os.rename(temp_combined, output_path)

            self.log_message("视频合并完成")
            return output_path

        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_combined):
                os.remove(temp_combined)
            raise e

    def add_background_music(self, video_path, music_path, output_path):
        """添加背景音乐"""
        if not os.path.exists(music_path):
            self.log_message(f"背景音乐文件不存在: {music_path}")
            return video_path

        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-i', music_path,
            '-c:v', 'copy',
            '-c:a', 'aac',
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-shortest',  # 以较短的流为准
            output_path
        ]

        self.log_message("添加背景音乐...")
        self.log_message(f"FFmpeg命令: {' '.join(cmd)}")

        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 universal_newlines=True, cwd=os.getcwd())
        _, stderr = process.communicate()

        if process.returncode != 0:
            raise RuntimeError(f"添加背景音乐失败: {stderr}")

        self.log_message("背景音乐添加完成")
        return output_path

    def start_video_creation(self):
        """开始视频制作"""
        def create_video():
            try:
                self.progress_var.set(0)
                self.log_message("开始视频制作...")

                # 检查FFmpeg是否可用
                try:
                    subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
                except (subprocess.CalledProcessError, FileNotFoundError):
                    raise RuntimeError("FFmpeg未安装或不在PATH中，请先安装FFmpeg")

                # 获取图片文件
                self.progress_var.set(10)
                image_files = self.get_image_files()
                self.log_message(f"找到 {len(image_files)} 张图片")

                # 创建临时滑动视频
                self.progress_var.set(30)
                temp_slideshow = "temp_slideshow.mp4"
                self.create_slideshow_video(image_files, temp_slideshow)

                # 合并插入视频
                self.progress_var.set(60)
                temp_combined = "temp_with_insert.mp4"
                insert_video_path = self.insert_video_var.get()
                if os.path.exists(insert_video_path):
                    self.combine_videos(temp_slideshow, insert_video_path, temp_combined)
                else:
                    temp_combined = temp_slideshow
                    self.log_message("跳过插入视频（文件不存在）")

                # 添加背景音乐
                self.progress_var.set(80)
                output_path = self.output_filename_var.get()
                music_path = self.background_music_var.get()
                if os.path.exists(music_path):
                    self.add_background_music(temp_combined, music_path, output_path)
                else:
                    # 如果没有背景音乐，直接重命名文件
                    if os.path.exists(output_path):
                        os.remove(output_path)
                    os.rename(temp_combined, output_path)
                    self.log_message("跳过背景音乐（文件不存在）")

                # 清理临时文件
                for temp_file in [temp_slideshow, temp_combined]:
                    if os.path.exists(temp_file) and temp_file != output_path:
                        os.remove(temp_file)

                self.progress_var.set(100)
                self.log_message(f"视频制作完成！输出文件: {output_path}")
                messagebox.showinfo("完成", f"视频制作完成！\n输出文件: {output_path}")

            except Exception as e:
                self.log_message(f"视频制作失败: {str(e)}")
                messagebox.showerror("错误", f"视频制作失败: {str(e)}")
                self.progress_var.set(0)

        # 在新线程中执行视频制作，避免界面冻结
        thread = threading.Thread(target=create_video)
        thread.daemon = True
        thread.start()


if __name__ == "__main__":
    root = tk.Tk()
    app = VideoMaker(root)
    root.mainloop()
