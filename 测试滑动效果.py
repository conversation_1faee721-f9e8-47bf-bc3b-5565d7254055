#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滑动效果
创建一个简单的滑动效果视频来验证功能
"""

import subprocess
import os
import json


def test_slide_effect():
    """测试滑动效果"""
    print("测试从右往左滑动效果...")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 图片文件夹
    img_folder = "2D Flat Rotatable Acrylic Desktop Ornament with colorful tropical fish design for home decoration, ideal for summer celebration gifts"
    
    # 获取第一张图片进行测试
    img_list = config.get('img_list', ['1'])
    first_img = os.path.join(img_folder, f"{img_list[0]}.jpg")
    
    if not os.path.exists(first_img):
        print(f"图片文件不存在: {first_img}")
        return False
    
    # 配置参数
    duration = config.get('duration', 2)
    scale = config.get('scale', '800:800')
    output_file = "test_slide.mp4"
    
    # 构建FFmpeg命令 - 单张图片滑动测试
    cmd = [
        'ffmpeg', '-y',
        '-loop', '1', '-t', str(duration),
        '-i', first_img,
        '-filter_complex',
        f'[0:v]scale={scale}[scaled];'
        f'[scaled]pad=iw*2:ih:iw:0:black[padded];'
        f'[padded]crop=w=800:h=800:x=800-800*t/{duration}:y=0[slide]',
        '-map', '[slide]',
        '-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-r', '30',
        output_file
    ]
    
    print("执行FFmpeg命令:")
    print(' '.join(cmd))
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 universal_newlines=True, encoding='utf-8', errors='ignore')
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print(f"✓ 滑动效果测试成功！")
                print(f"  输出文件: {output_file} ({file_size:.1f} MB)")
                print(f"  效果: 图片从右往左滑动 {duration} 秒")
                return True
            else:
                print("✗ 输出文件未生成")
                return False
        else:
            print("✗ FFmpeg执行失败")
            if stderr:
                print(f"错误信息: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 执行出错: {e}")
        return False


def test_multiple_slides():
    """测试多张图片连续滑动"""
    print("\n测试多张图片连续滑动...")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 图片文件夹
    img_folder = "2D Flat Rotatable Acrylic Desktop Ornament with colorful tropical fish design for home decoration, ideal for summer celebration gifts"
    
    # 获取配置中的图片列表
    img_list = config.get('img_list', ['1', '5', '7'])
    image_files = []
    
    for img_num in img_list:
        img_path = os.path.join(img_folder, f"{img_num}.jpg")
        if os.path.exists(img_path):
            image_files.append(img_path)
    
    if len(image_files) < 2:
        print("需要至少2张图片来测试连续滑动")
        return False
    
    # 配置参数
    duration = config.get('duration', 2)
    scale = config.get('scale', '800:800')
    output_file = "test_multi_slide.mp4"
    
    # 构建FFmpeg命令
    cmd = ['ffmpeg', '-y']
    
    # 添加输入图片
    for img in image_files:
        cmd.extend(['-loop', '1', '-t', str(duration), '-i', img])
    
    # 构建滤镜链
    filter_parts = []
    
    for i, _ in enumerate(image_files):
        # 缩放图片
        filter_parts.append(f"[{i}:v]scale={scale}[scaled{i}]")
        # 创建滑动效果
        filter_parts.append(f"[scaled{i}]pad=iw*2:ih:iw:0:black[padded{i}]")
        filter_parts.append(f"[padded{i}]crop=w=800:h=800:x=800-800*t/{duration}:y=0[slide{i}]")
    
    # 连接所有滑动片段
    concat_inputs = "".join([f"[slide{i}]" for i in range(len(image_files))])
    filter_parts.append(f"{concat_inputs}concat=n={len(image_files)}:v=1:a=0[outv]")
    
    cmd.extend(['-filter_complex', ';'.join(filter_parts)])
    cmd.extend(['-map', '[outv]'])
    cmd.extend(['-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-r', '30'])
    cmd.append(output_file)
    
    print("执行FFmpeg命令:")
    print(' '.join(cmd))
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 universal_newlines=True, encoding='utf-8', errors='ignore')
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                total_duration = len(image_files) * duration
                print(f"✓ 多图片滑动测试成功！")
                print(f"  输出文件: {output_file} ({file_size:.1f} MB)")
                print(f"  包含 {len(image_files)} 张图片，总时长 {total_duration} 秒")
                print(f"  效果: 每张图片从右往左滑动")
                return True
            else:
                print("✗ 输出文件未生成")
                return False
        else:
            print("✗ FFmpeg执行失败")
            if stderr:
                print(f"错误信息: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 执行出错: {e}")
        return False


def main():
    """主函数"""
    print("=== 滑动效果测试 ===")
    
    # 测试单张图片滑动
    test1_ok = test_slide_effect()
    
    # 测试多张图片连续滑动
    test2_ok = test_multiple_slides()
    
    print("\n=== 测试结果 ===")
    print(f"单张图片滑动: {'✓ 成功' if test1_ok else '✗ 失败'}")
    print(f"多张图片滑动: {'✓ 成功' if test2_ok else '✗ 失败'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 所有滑动效果测试通过！")
        print("现在可以在主程序中使用滑动效果了。")
    else:
        print("\n⚠️ 部分测试失败，请检查FFmpeg命令和参数。")


if __name__ == "__main__":
    main()
